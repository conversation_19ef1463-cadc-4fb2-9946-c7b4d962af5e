<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D翻转登录页面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        /* 背景动画效果 */
        .bg-animation {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }

        .bg-animation span {
            position: absolute;
            display: block;
            width: 20px;
            height: 20px;
            background: rgba(255, 255, 255, 0.1);
            animation: move 25s linear infinite;
            bottom: -150px;
        }

        .bg-animation span:nth-child(1) {
            left: 25%;
            width: 80px;
            height: 80px;
            animation-delay: 0s;
        }

        .bg-animation span:nth-child(2) {
            left: 10%;
            width: 20px;
            height: 20px;
            animation-delay: 2s;
            animation-duration: 12s;
        }

        .bg-animation span:nth-child(3) {
            left: 70%;
            width: 20px;
            height: 20px;
            animation-delay: 4s;
        }

        .bg-animation span:nth-child(4) {
            left: 40%;
            width: 60px;
            height: 60px;
            animation-delay: 0s;
            animation-duration: 18s;
        }

        .bg-animation span:nth-child(5) {
            left: 65%;
            width: 20px;
            height: 20px;
            animation-delay: 0s;
        }

        .bg-animation span:nth-child(6) {
            left: 75%;
            width: 110px;
            height: 110px;
            animation-delay: 3s;
        }

        .bg-animation span:nth-child(7) {
            left: 35%;
            width: 150px;
            height: 150px;
            animation-delay: 7s;
        }

        .bg-animation span:nth-child(8) {
            left: 50%;
            width: 25px;
            height: 25px;
            animation-delay: 15s;
            animation-duration: 45s;
        }

        .bg-animation span:nth-child(9) {
            left: 20%;
            width: 15px;
            height: 15px;
            animation-delay: 2s;
            animation-duration: 35s;
        }

        .bg-animation span:nth-child(10) {
            left: 85%;
            width: 150px;
            height: 150px;
            animation-delay: 0s;
            animation-duration: 11s;
        }

        @keyframes move {
            0% {
                transform: translateY(0) rotate(0deg);
                opacity: 1;
                border-radius: 0;
            }
            100% {
                transform: translateY(-1000px) rotate(720deg);
                opacity: 0;
                border-radius: 50%;
            }
        }

        /* 3D卡片容器 */
        .card-container {
            perspective: 1000px;
            width: 400px;
            height: 600px;
            position: relative;
        }

        .card {
            width: 100%;
            height: 100%;
            position: absolute;
            transform-style: preserve-3d;
            transition: transform 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
        }

        .card-face {
            position: absolute;
            width: 100%;
            height: 100%;
            backface-visibility: hidden;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .card-back {
            transform: rotateY(180deg);
        }

        /* 卡片翻转状态 */
        .card.show-register {
            transform: rotateY(180deg);
        }

        .card.show-forgot {
            transform: rotateY(360deg);
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
            position: relative;
        }

        .form-input {
            width: 100%;
            padding: 15px 20px 15px 50px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
        }

        .form-icon {
            position: absolute;
            left: 18px;
            top: 50%;
            transform: translateY(-50%);
            color: #667eea;
            font-size: 18px;
        }

        .btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: transparent;
            color: #667eea;
            border: 2px solid #667eea;
        }

        .btn-secondary:hover {
            background: #667eea;
            color: white;
        }

        /* 导航标签 */
        .tab-nav {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
        }

        .tab-btn {
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 25px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .tab-btn.active {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .tab-btn:hover {
            background: rgba(255, 255, 255, 0.25);
        }

        /* 标题样式 */
        .form-title {
            text-align: center;
            margin-bottom: 10px;
            color: #333;
            font-size: 28px;
            font-weight: 700;
        }

        .form-subtitle {
            text-align: center;
            margin-bottom: 30px;
            color: #666;
            font-size: 14px;
        }

        /* 社交登录 */
        .social-login {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
        }

        .social-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 2px solid #e1e5e9;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #333;
        }

        .social-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .social-btn.google:hover {
            border-color: #db4437;
            color: #db4437;
        }

        .social-btn.github:hover {
            border-color: #333;
            color: #333;
        }

        .social-btn.apple:hover {
            border-color: #000;
            color: #000;
        }

        /* 分割线 */
        .divider {
            text-align: center;
            margin: 20px 0;
            position: relative;
            color: #666;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e1e5e9;
        }

        .divider span {
            background: rgba(255, 255, 255, 0.95);
            padding: 0 15px;
            position: relative;
        }

        /* 链接样式 */
        .form-links {
            text-align: center;
            margin-top: 20px;
        }

        .form-links a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .form-links a:hover {
            color: #764ba2;
        }

        /* 复选框样式 */
        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .checkbox-group input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
        }

        .checkbox-group label {
            color: #666;
            font-size: 14px;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .card-container {
                width: 90vw;
                height: 80vh;
            }

            .card-face {
                padding: 30px 20px;
            }

            .form-title {
                font-size: 24px;
            }

            .tab-nav {
                gap: 10px;
            }

            .tab-btn {
                padding: 8px 16px;
                font-size: 14px;
            }
        }

        /* 密码显示/隐藏按钮 */
        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #667eea;
            cursor: pointer;
            font-size: 16px;
        }

        /* 成功消息样式 */
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
            display: none;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
            display: none;
        }
    </style>
</head>
<body>
    <!-- 背景动画 -->
    <div class="bg-animation">
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
    </div>

    <!-- 导航标签 -->
    <div class="tab-nav">
        <button class="tab-btn active" onclick="showCard('login')">
            <i class="fas fa-sign-in-alt mr-2"></i>登录
        </button>
        <button class="tab-btn" onclick="showCard('register')">
            <i class="fas fa-user-plus mr-2"></i>注册
        </button>
        <button class="tab-btn" onclick="showCard('forgot')">
            <i class="fas fa-key mr-2"></i>忘记密码
        </button>
    </div>

    <!-- 3D卡片容器 -->
    <div class="card-container">
        <div class="card" id="card">
            <!-- 登录页面 -->
            <div class="card-face">
                <div class="success-message" id="loginSuccess">
                    <i class="fas fa-check-circle mr-2"></i>
                    登录成功！正在跳转...
                </div>
                <div class="error-message" id="loginError">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    邮箱或密码错误，请重试
                </div>

                <h2 class="form-title">欢迎回来</h2>
                <p class="form-subtitle">请登录您的账户继续使用</p>

                <form id="loginForm">
                    <div class="form-group">
                        <i class="fas fa-envelope form-icon"></i>
                        <input type="email" class="form-input" placeholder="邮箱地址" required>
                    </div>

                    <div class="form-group">
                        <i class="fas fa-lock form-icon"></i>
                        <input type="password" class="form-input" placeholder="密码" required>
                        <button type="button" class="password-toggle" onclick="togglePassword(this)">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="remember">
                        <label for="remember">记住我</label>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt mr-2"></i>登录
                    </button>
                </form>

                <div class="divider">
                    <span>或</span>
                </div>

                <div class="social-login">
                    <a href="#" class="social-btn google">
                        <i class="fab fa-google"></i>
                    </a>
                    <a href="#" class="social-btn github">
                        <i class="fab fa-github"></i>
                    </a>
                    <a href="#" class="social-btn apple">
                        <i class="fab fa-apple"></i>
                    </a>
                </div>

                <div class="form-links">
                    <p>还没有账户？<a href="#" onclick="showCard('register')">立即注册</a></p>
                </div>
            </div>

            <!-- 注册页面 -->
            <div class="card-face card-back">
                <div class="success-message" id="registerSuccess">
                    <i class="fas fa-check-circle mr-2"></i>
                    注册成功！请检查邮箱验证
                </div>
                <div class="error-message" id="registerError">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    注册失败，请检查信息后重试
                </div>

                <h2 class="form-title">创建账户</h2>
                <p class="form-subtitle">加入我们，开启精彩旅程</p>

                <form id="registerForm">
                    <div class="form-group">
                        <i class="fas fa-user form-icon"></i>
                        <input type="text" class="form-input" placeholder="姓名" required>
                    </div>

                    <div class="form-group">
                        <i class="fas fa-envelope form-icon"></i>
                        <input type="email" class="form-input" placeholder="邮箱地址" required>
                    </div>

                    <div class="form-group">
                        <i class="fas fa-phone form-icon"></i>
                        <input type="tel" class="form-input" placeholder="手机号码" required>
                    </div>

                    <div class="form-group">
                        <i class="fas fa-lock form-icon"></i>
                        <input type="password" class="form-input" placeholder="密码" required>
                        <button type="button" class="password-toggle" onclick="togglePassword(this)">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>

                    <div class="form-group">
                        <i class="fas fa-lock form-icon"></i>
                        <input type="password" class="form-input" placeholder="确认密码" required>
                        <button type="button" class="password-toggle" onclick="togglePassword(this)">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="terms" required>
                        <label for="terms">我同意<a href="#">服务条款</a>和<a href="#">隐私政策</a></label>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-user-plus mr-2"></i>创建账户
                    </button>
                </form>

                <div class="form-links">
                    <p>已有账户？<a href="#" onclick="showCard('login')">立即登录</a></p>
                </div>
            </div>

            <!-- 忘记密码页面 -->
            <div class="card-face" style="transform: rotateY(360deg);">
                <div class="success-message" id="forgotSuccess">
                    <i class="fas fa-check-circle mr-2"></i>
                    重置链接已发送到您的邮箱
                </div>
                <div class="error-message" id="forgotError">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    邮箱地址不存在，请检查后重试
                </div>

                <h2 class="form-title">找回密码</h2>
                <p class="form-subtitle">请输入您的邮箱地址，我们将发送重置链接</p>

                <form id="forgotForm">
                    <div class="form-group">
                        <i class="fas fa-envelope form-icon"></i>
                        <input type="email" class="form-input" placeholder="邮箱地址" required>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane mr-2"></i>发送重置链接
                    </button>
                </form>

                <div class="form-links">
                    <p>记起密码了？<a href="#" onclick="showCard('login')">返回登录</a></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentCard = 'login';

        function showCard(cardType) {
            const card = document.getElementById('card');
            const tabBtns = document.querySelectorAll('.tab-btn');
            
            // 移除所有按钮的active状态
            tabBtns.forEach(btn => btn.classList.remove('active'));
            
            // 隐藏所有消息
            hideAllMessages();
            
            // 根据卡片类型设置旋转角度
            switch(cardType) {
                case 'login':
                    card.style.transform = 'rotateY(0deg)';
                    tabBtns[0].classList.add('active');
                    break;
                case 'register':
                    card.style.transform = 'rotateY(180deg)';
                    tabBtns[1].classList.add('active');
                    break;
                case 'forgot':
                    card.style.transform = 'rotateY(360deg)';
                    tabBtns[2].classList.add('active');
                    break;
            }
            
            currentCard = cardType;
        }

        function togglePassword(button) {
            const input = button.parentElement.querySelector('input');
            const icon = button.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        function hideAllMessages() {
            const messages = document.querySelectorAll('.success-message, .error-message');
            messages.forEach(msg => msg.style.display = 'none');
        }

        function showMessage(type, formType) {
            hideAllMessages();
            const messageElement = document.getElementById(formType + (type === 'success' ? 'Success' : 'Error'));
            if (messageElement) {
                messageElement.style.display = 'block';
                setTimeout(() => {
                    messageElement.style.display = 'none';
                }, 3000);
            }
        }

        // 表单提交处理
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 模拟登录验证
            const email = this.querySelector('input[type="email"]').value;
            const password = this.querySelector('input[type="password"]').value;
            
            if (email && password) {
                showMessage('success', 'login');
                setTimeout(() => {
                    alert('登录成功！这里通常会跳转到主页');
                }, 1500);
            } else {
                showMessage('error', 'login');
            }
        });

        document.getElementById('registerForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 模拟注册验证
            const name = this.querySelector('input[type="text"]').value;
            const email = this.querySelector('input[type="email"]').value;
            const phone = this.querySelector('input[type="tel"]').value;
            const password = this.querySelector('input[type="password"]').value;
            const confirmPassword = this.querySelectorAll('input[type="password"]')[1].value;
            const terms = this.querySelector('input[type="checkbox"]').checked;
            
            if (name && email && phone && password && confirmPassword && terms) {
                if (password === confirmPassword) {
                    showMessage('success', 'register');
                    setTimeout(() => {
                        showCard('login');
                    }, 2000);
                } else {
                    alert('密码和确认密码不匹配');
                }
            } else {
                showMessage('error', 'register');
            }
        });

        document.getElementById('forgotForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 模拟密码重置
            const email = this.querySelector('input[type="email"]').value;
            
            if (email) {
                showMessage('success', 'forgot');
                setTimeout(() => {
                    showCard('login');
                }, 2000);
            } else {
                showMessage('error', 'forgot');
            }
        });

        // 添加输入框聚焦效果
        document.querySelectorAll('.form-input').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'translateY(-2px)';
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'translateY(0)';
            });
        });

        // 添加社交按钮点击效果
        document.querySelectorAll('.social-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                const platform = this.classList.contains('google') ? 'Google' : 
                                this.classList.contains('github') ? 'GitHub' : 'Apple';
                alert(`${platform} 登录功能需要后端支持`);
            });
        });

        // 键盘导航支持
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Tab') {
                // 允许默认Tab行为
                return;
            }
            
            switch(e.key) {
                case '1':
                    showCard('login');
                    break;
                case '2':
                    showCard('register');
                    break;
                case '3':
                    showCard('forgot');
                    break;
            }
        });
    </script>
</body>
</html>