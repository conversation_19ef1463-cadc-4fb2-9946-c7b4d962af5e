<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录页面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .glass-morphism {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .floating-animation {
            animation: floating 3s ease-in-out infinite;
        }

        @keyframes floating {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        .input-focus:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .tab-button {
            position: relative;
            transition: all 0.3s ease;
        }

        .tab-button.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 2px;
            background: white;
            border-radius: 1px;
        }

        .mobile-frame {
            width: 375px;
            height: 812px;
            max-width: 100%;
            max-height: 90vh;
        }

        @media (max-width: 640px) {
            .mobile-frame {
                height: 100vh;
                max-height: 100vh;
            }
        }
    </style>
</head>

<body class="gradient-bg min-h-screen flex items-center justify-center p-4">
    <!-- Mobile Frame -->
    <div class="bg-black rounded-3xl p-2 shadow-2xl mobile-frame">
        <div class="bg-white rounded-2xl overflow-hidden h-full">
            <!-- Status Bar -->
            <div class="bg-black text-white text-xs flex justify-between items-center px-6 py-2">
                <span>9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-white text-xs"></i>
                    <i class="fas fa-wifi text-white text-xs"></i>
                    <i class="fas fa-battery-three-quarters text-white text-xs"></i>
                </div>
            </div>

            <!-- Main Content -->
            <div class="gradient-bg h-full relative overflow-hidden">
                <!-- Floating Elements -->
                <div class="absolute top-20 left-10 w-20 h-20 glass-morphism rounded-full floating-animation"></div>
                <div class="absolute top-40 right-8 w-12 h-12 glass-morphism rounded-full floating-animation" style="animation-delay: 1s;"></div>
                <div class="absolute bottom-40 left-6 w-16 h-16 glass-morphism rounded-full floating-animation" style="animation-delay: 2s;"></div>

                <!-- Tab Navigation -->
                <div class="flex justify-center space-x-8 pt-8 pb-4">
                    <button class="tab-button active text-white font-medium text-sm pb-2" data-tab="login">
                        登录
                    </button>
                    <button class="tab-button text-white text-opacity-70 text-sm pb-2 hover:text-opacity-100 transition-all" data-tab="register">
                        注册
                    </button>
                    <button class="tab-button text-white text-opacity-70 text-sm pb-2 hover:text-opacity-100 transition-all" data-tab="forgot">
                        忘记密码
                    </button>
                </div>

                <!-- Tab Content Container -->
                <div class="flex flex-col justify-center items-center h-full px-8 pb-8">

                    <!-- Login Tab -->
                    <div id="login" class="tab-content active w-full max-w-sm">
                        <!-- Logo/Icon -->
                        <div class="glass-morphism rounded-full p-6 mb-8">
                            <div class="w-12 h-12 bg-white rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-indigo-600 text-2xl"></i>
                            </div>
                        </div>

                        <!-- Welcome Text -->
                        <div class="text-center mb-8">
                            <h1 class="text-white text-2xl font-bold mb-2">欢迎回来</h1>
                            <p class="text-white text-opacity-80 text-sm">请登录您的账户继续使用</p>
                        </div>

                        <!-- Login Form -->
                        <div class="glass-morphism rounded-2xl p-6 w-full">
                            <form class="space-y-4" id="loginForm">
                                <!-- Email Input -->
                                <div class="relative">
                                    <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
                                        <i class="fas fa-envelope text-white opacity-70"></i>
                                    </div>
                                    <input type="email" placeholder="邮箱地址" required
                                        class="input-focus w-full pl-12 pr-4 py-3 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-xl text-white placeholder-white placeholder-opacity-70 focus:outline-none transition-all duration-300">
                                </div>

                                <!-- Password Input -->
                                <div class="relative">
                                    <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
                                        <i class="fas fa-lock text-white opacity-70"></i>
                                    </div>
                                    <input type="password" placeholder="密码" required
                                        class="input-focus w-full pl-12 pr-12 py-3 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-xl text-white placeholder-white placeholder-opacity-70 focus:outline-none transition-all duration-300">
                                    <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                                        <i class="fas fa-eye-slash text-white opacity-50 cursor-pointer toggle-password"></i>
                                    </div>
                                </div>

                                <!-- Remember Me & Forgot Password -->
                                <div class="flex items-center justify-between text-sm">
                                    <label class="flex items-center text-white text-opacity-80">
                                        <input type="checkbox" class="mr-2 rounded border-white border-opacity-30">
                                        记住我
                                    </label>
                                    <a href="#" class="text-white text-opacity-80 hover:text-white transition-colors">
                                        忘记密码？
                                    </a>
                                </div>

                                <!-- Login Button -->
                                <button type="submit"
                                    class="w-full py-3 bg-white bg-opacity-20 hover:bg-opacity-30 text-white font-semibold rounded-xl transition-all duration-300 border border-white border-opacity-30 hover:border-opacity-50">
                                    登录
                                </button>
                            </form>
                        </div>

                        <!-- Divider -->
                        <div class="flex items-center my-6 w-full">
                            <div class="flex-1 h-px bg-white bg-opacity-30"></div>
                            <span class="px-4 text-white text-opacity-70 text-sm">或</span>
                            <div class="flex-1 h-px bg-white bg-opacity-30"></div>
                        </div>

                        <!-- Social Login -->
                        <div class="flex space-x-4 mb-6">
                            <button class="glass-morphism p-3 rounded-xl hover:bg-opacity-20 transition-all duration-300">
                                <i class="fab fa-github text-white text-xl"></i>
                            </button>
                            <button class="glass-morphism p-3 rounded-xl hover:bg-opacity-20 transition-all duration-300">
                                <i class="fab fa-google text-white text-xl"></i>
                            </button>
                            <button class="glass-morphism p-3 rounded-xl hover:bg-opacity-20 transition-all duration-300">
                                <i class="fas fa-mobile-alt text-white text-xl"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Register Tab -->
                    <div id="register" class="tab-content w-full max-w-sm">
                        <!-- Logo/Icon -->
                        <div class="glass-morphism rounded-full p-6 mb-6">
                            <div class="w-12 h-12 bg-white rounded-full flex items-center justify-center">
                                <i class="fas fa-user-plus text-indigo-600 text-2xl"></i>
                            </div>
                        </div>

                        <!-- Welcome Text -->
                        <div class="text-center mb-6">
                            <h1 class="text-white text-2xl font-bold mb-2">创建账户</h1>
                            <p class="text-white text-opacity-80 text-sm">加入我们，开启精彩旅程</p>
                        </div>

                        <!-- Register Form -->
                        <div class="glass-morphism rounded-2xl p-6 w-full">
                            <form class="space-y-4" id="registerForm">
                                <!-- Name Input -->
                                <div class="relative">
                                    <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
                                        <i class="fas fa-user text-white opacity-70"></i>
                                    </div>
                                    <input type="text" placeholder="姓名" required
                                        class="input-focus w-full pl-12 pr-4 py-3 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-xl text-white placeholder-white placeholder-opacity-70 focus:outline-none transition-all duration-300">
                                </div>

                                <!-- Email Input -->
                                <div class="relative">
                                    <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
                                        <i class="fas fa-envelope text-white opacity-70"></i>
                                    </div>
                                    <input type="email" placeholder="邮箱地址" required
                                        class="input-focus w-full pl-12 pr-4 py-3 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-xl text-white placeholder-white placeholder-opacity-70 focus:outline-none transition-all duration-300">
                                </div>

                                <!-- Phone Input -->
                                <div class="relative">
                                    <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
                                        <i class="fas fa-phone text-white opacity-70"></i>
                                    </div>
                                    <input type="tel" placeholder="手机号码" required
                                        class="input-focus w-full pl-12 pr-4 py-3 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-xl text-white placeholder-white placeholder-opacity-70 focus:outline-none transition-all duration-300">
                                </div>

                                <!-- Password Input -->
                                <div class="relative">
                                    <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
                                        <i class="fas fa-lock text-white opacity-70"></i>
                                    </div>
                                    <input type="password" placeholder="密码" required
                                        class="input-focus w-full pl-12 pr-12 py-3 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-xl text-white placeholder-white placeholder-opacity-70 focus:outline-none transition-all duration-300">
                                    <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                                        <i class="fas fa-eye-slash text-white opacity-50 cursor-pointer toggle-password"></i>
                                    </div>
                                </div>

                                <!-- Confirm Password Input -->
                                <div class="relative">
                                    <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
                                        <i class="fas fa-lock text-white opacity-70"></i>
                                    </div>
                                    <input type="password" placeholder="确认密码" required
                                        class="input-focus w-full pl-12 pr-12 py-3 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-xl text-white placeholder-white placeholder-opacity-70 focus:outline-none transition-all duration-300">
                                    <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                                        <i class="fas fa-eye-slash text-white opacity-50 cursor-pointer toggle-password"></i>
                                    </div>
                                </div>

                                <!-- Terms and Conditions -->
                                <div class="flex items-start text-xs text-white text-opacity-80 mt-4">
                                    <input type="checkbox" class="mr-2 mt-0.5 rounded border-white border-opacity-30" required>
                                    <span>我已阅读并同意<a href="#" class="text-white font-semibold ml-1 hover:underline">服务条款</a>和<a href="#" class="text-white font-semibold ml-1 hover:underline">隐私政策</a></span>
                                </div>

                                <!-- Register Button -->
                                <button type="submit"
                                    class="w-full py-3 bg-white bg-opacity-20 hover:bg-opacity-30 text-white font-semibold rounded-xl transition-all duration-300 border border-white border-opacity-30 hover:border-opacity-50 mt-4">
                                    创建账户
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Forgot Password Tab -->
                    <div id="forgot" class="tab-content w-full max-w-sm">
                        <!-- Logo/Icon -->
                        <div class="glass-morphism rounded-full p-6 mb-8">
                            <div class="w-12 h-12 bg-white rounded-full flex items-center justify-center">
                                <i class="fas fa-key text-indigo-600 text-2xl"></i>
                            </div>
                        </div>

                        <!-- Title Text -->
                        <div class="text-center mb-8">
                            <h1 class="text-white text-2xl font-bold mb-2">找回密码</h1>
                            <p class="text-white text-opacity-80 text-sm">请输入您的邮箱地址<br>我们将发送重置链接到您的邮箱</p>
                        </div>

                        <!-- Forgot Password Form -->
                        <div class="glass-morphism rounded-2xl p-6 w-full">
                            <form class="space-y-6" id="forgotForm">
                                <!-- Email Input -->
                                <div class="relative">
                                    <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
                                        <i class="fas fa-envelope text-white opacity-70"></i>
                                    </div>
                                    <input type="email" placeholder="邮箱地址" required
                                        class="input-focus w-full pl-12 pr-4 py-3 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-xl text-white placeholder-white placeholder-opacity-70 focus:outline-none transition-all duration-300">
                                </div>

                                <!-- Send Reset Link Button -->
                                <button type="submit"
                                    class="w-full py-3 bg-white bg-opacity-20 hover:bg-opacity-30 text-white font-semibold rounded-xl transition-all duration-300 border border-white border-opacity-30 hover:border-opacity-50">
                                    发送重置链接
                                </button>
                            </form>
                        </div>

                        <!-- Helper Text -->
                        <div class="text-center text-white text-opacity-70 text-xs max-w-sm mt-6">
                            <p>重置链接将以邮件形式发送到您的邮箱</p>
                            <p class="mt-2">如果您无法接收邮件，请联系客服</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Tab switching functionality
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const targetTab = this.getAttribute('data-tab');
                    
                    // Remove active class from all buttons and contents
                    tabButtons.forEach(btn => {
                        btn.classList.remove('active');
                        btn.classList.add('text-white', 'text-opacity-70');
                        btn.classList.remove('text-opacity-100');
                    });
                    
                    tabContents.forEach(content => {
                        content.classList.remove('active');
                    });
                    
                    // Add active class to clicked button and corresponding content
                    this.classList.add('active');
                    this.classList.remove('text-opacity-70');
                    this.classList.add('text-opacity-100');
                    
                    document.getElementById(targetTab).classList.add('active');
                });
            });

            // Password visibility toggle
            const togglePasswordButtons = document.querySelectorAll('.toggle-password');
            togglePasswordButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const input = this.parentElement.querySelector('input');
                    const icon = this;
                    
                    if (input.type === 'password') {
                        input.type = 'text';
                        icon.classList.remove('fa-eye-slash');
                        icon.classList.add('fa-eye');
                    } else {
                        input.type = 'password';
                        icon.classList.remove('fa-eye');
                        icon.classList.add('fa-eye-slash');
                    }
                });
            });

            // Form submissions
            const loginForm = document.getElementById('loginForm');
            const registerForm = document.getElementById('registerForm');
            const forgotForm = document.getElementById('forgotForm');

            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                // Here you would normally send the login data to your server
                alert('登录功能需要后端支持');
            });

            registerForm.addEventListener('submit', function(e) {
                e.preventDefault();
                // Here you would normally send the registration data to your server
                alert('注册功能需要后端支持');
            });

            forgotForm.addEventListener('submit', function(e) {
                e.preventDefault();
                // Here you would normally send the password reset request to your server
                alert('密码重置功能需要后端支持');
            });

            // Input focus effects
            const inputs = document.querySelectorAll('input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'scale(1.02)';
                });
                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'scale(1)';
                });
            });
        });
    </script>
</body>

</html>