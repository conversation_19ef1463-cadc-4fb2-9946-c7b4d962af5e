<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Baby.AI - 智能育儿助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .glass-effect {
            background: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-[#FFE5CC] to-[#66CCFF] min-h-screen p-8">
    <!-- 设备模拟框架 -->
    <div class="max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-8">
        <!-- 成长追踪主页 -->
        <div class="bg-white rounded-[40px] p-6 shadow-2xl overflow-hidden relative h-[700px] glass-effect">
            <div class="h-full flex flex-col p-6">
                <!-- 顶部信息栏 -->
                <div class="flex items-center justify-between mb-8">
                    <div class="flex items-center space-x-3">
                        <img src="https://images.unsplash.com/photo-1492725764893-90b379c2b6e7?w=500" alt="Baby" class="w-10 h-10 rounded-full object-cover">
                        <div>
                            <h2 class="text-lg font-bold text-[#333333]">小明</h2>
                            <p class="text-sm text-gray-500">8个月</p>
                        </div>
                    </div>
                    <button class="p-2 rounded-xl hover:bg-gray-100 transition">
                        <img src="https://unpkg.com/lucide-static@latest/icons/settings.svg" class="w-6 h-6 text-gray-400" alt="Settings">
                    </button>
                </div>

                <!-- 成长数据概览 -->
                <div class="bg-gradient-to-r from-[#FFE5CC] to-[#FFD9B3] rounded-2xl p-6 mb-6">
                    <h3 class="text-lg font-bold text-[#FF9966] mb-4">成长数据</h3>
                    <div class="grid grid-cols-3 gap-4">
                        <div class="bg-white/80 rounded-xl p-4 text-center">
                            <p class="text-sm text-gray-500 mb-1">身高</p>
                            <p class="text-xl font-bold text-[#333333]">68<span class="text-sm font-normal">cm</span></p>
                            <p class="text-xs text-[#FF9966]">+2cm</p>
                        </div>
                        <div class="bg-white/80 rounded-xl p-4 text-center">
                            <p class="text-sm text-gray-500 mb-1">体重</p>
                            <p class="text-xl font-bold text-[#333333]">8.5<span class="text-sm font-normal">kg</span></p>
                            <p class="text-xs text-[#FF9966]">+0.5kg</p>
                        </div>
                        <div class="bg-white/80 rounded-xl p-4 text-center">
                            <p class="text-sm text-gray-500 mb-1">头围</p>
                            <p class="text-xl font-bold text-[#333333]">44<span class="text-sm font-normal">cm</span></p>
                            <p class="text-xs text-[#FF9966]">+0.8cm</p>
                        </div>
                    </div>
                </div>

                <!-- 发展能力雷达图 -->
                <div class="bg-gradient-to-r from-[#E5F5FF] to-[#CCE9FF] rounded-2xl p-6 mb-6">
                    <h3 class="text-lg font-bold text-[#66CCFF] mb-4">发展能力</h3>
                    <div class="relative h-48 w-full">
                        <!-- 模拟雷达图 -->
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="w-32 h-32 rounded-full border-2 border-[#66CCFF]/20"></div>
                            <div class="absolute w-24 h-24 rounded-full border-2 border-[#66CCFF]/40"></div>
                            <div class="absolute w-16 h-16 rounded-full border-2 border-[#66CCFF]/60"></div>
                            <div class="absolute w-8 h-8 rounded-full bg-[#66CCFF]/20"></div>
                            
                            <!-- 能力点 -->
                            <div class="absolute top-1/4 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-[#FF9966] rounded-full shadow-lg"></div>
                            <div class="absolute top-1/2 left-3/4 transform -translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-[#FF9966] rounded-full shadow-lg"></div>
                            <div class="absolute top-3/4 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-[#FF9966] rounded-full shadow-lg"></div>
                            <div class="absolute top-1/2 left-1/4 transform -translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-[#FF9966] rounded-full shadow-lg"></div>
                            <div class="absolute top-1/3 left-1/3 transform -translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-[#FF9966] rounded-full shadow-lg"></div>
                        </div>
                        
                        <!-- 能力标签 -->
                        <div class="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/4">
                            <span class="text-xs font-medium text-[#66CCFF]">运动能力</span>
                        </div>
                        <div class="absolute top-1/2 right-0 transform translate-x-1/4 -translate-y-1/2">
                            <span class="text-xs font-medium text-[#66CCFF]">语言能力</span>
                        </div>
                        <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/4">
                            <span class="text-xs font-medium text-[#66CCFF]">认知能力</span>
                        </div>
                        <div class="absolute top-1/2 left-0 transform -translate-x-1/4 -translate-y-1/2">
                            <span class="text-xs font-medium text-[#66CCFF]">社交能力</span>
                        </div>
                        <div class="absolute top-1/4 left-1/4 transform -translate-x-1/2 -translate-y-1/2">
                            <span class="text-xs font-medium text-[#66CCFF]">情绪控制</span>
                        </div>
                    </div>
                </div>

                <!-- 里程碑 -->
                <div class="flex-1 overflow-hidden">
                    <h3 class="text-lg font-bold text-[#333333] mb-4">发展里程碑</h3>
                    <div class="space-y-4 overflow-y-auto h-[calc(100%-2rem)] pr-2">
                        <div class="p-4 rounded-2xl bg-gray-50 border-l-4 border-[#66CCFF]">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-[#66CCFF]">本月目标</span>
                                <span class="text-xs px-2 py-1 rounded-full bg-[#E5F5FF] text-[#66CCFF]">进行中</span>
                            </div>
                            <p class="text-gray-600 text-sm">学会爬行</p>
                        </div>
                        <div class="p-4 rounded-2xl bg-gray-50 border-l-4 border-[#FF9966]">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-[#FF9966]">已达成</span>
                                <span class="text-xs px-2 py-1 rounded-full bg-[#FFE5CC] text-[#FF9966]">7个月</span>
                            </div>
                            <p class="text-gray-600 text-sm">能够独立坐立</p>
                        </div>
                        <div class="p-4 rounded-2xl bg-gray-50 border-l-4 border-[#FF9966]">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-[#FF9966]">已达成</span>
                                <span class="text-xs px-2 py-1 rounded-full bg-[#FFE5CC] text-[#FF9966]">6个月</span>
                            </div>
                            <p class="text-gray-600 text-sm">开始添加辅食</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 发展详情页面 -->
        <div class="bg-white rounded-[40px] p-6 shadow-2xl overflow-hidden relative h-[700px] glass-effect">
            <div class="h-full flex flex-col p-6">
                <!-- 顶部信息 -->
                <div class="flex items-center space-x-3 mb-8">
                    <button class="p-2 rounded-xl hover:bg-gray-100 transition">
                        <img src="https://unpkg.com/lucide-static@latest/icons/arrow-left.svg" class="w-6 h-6" alt="Back">
                    </button>
                    <div>
                        <h2 class="text-lg font-bold text-[#333333]">运动能力</h2>
                        <p class="text-sm text-gray-500">8个月发展情况</p>
                    </div>
                </div>

                <!-- 能力详情 -->
                <div class="flex-1 overflow-y-auto pr-2">
                    <div class="space-y-6">
                        <!-- 能力评估 -->
                        <div class="bg-[#E5F5FF] rounded-2xl p-6">
                            <h3 class="text-lg font-medium text-[#66CCFF] mb-4">当前评估</h3>
                            <div class="flex items-center mb-4">
                                <div class="w-full bg-white rounded-full h-2.5">
                                    <div class="bg-[#66CCFF] h-2.5 rounded-full" style="width: 75%"></div>
                                </div>
                                <span class="ml-4 text-lg font-bold text-[#66CCFF]">75%</span>
                            </div>
                            <p class="text-sm text-gray-600">小明的运动能力发展良好，已经能够独立坐立，并开始尝试爬行。</p>
                        </div>

                        <!-- 已达成里程碑 -->
                        <div>
                            <h3 class="text-md font-medium text-gray-600 mb-4">已达成里程碑</h3>
                            <ul class="space-y-4">
                                <li class="flex items-start space-x-3">
                                    <div class="w-6 h-6 rounded-full bg-[#FFE5CC] flex items-center justify-center flex-shrink-0 mt-0.5">
                                        <img src="https://unpkg.com/lucide-static@latest/icons/check.svg" class="w-4 h-4 text-[#FF9966]" alt="Check">
                                    </div>
                                    <div>
                                        <p class="text-gray-600 font-medium">独立坐立</p>
                                        <p class="text-sm text-gray-500">7个月时达成</p>
                                    </div>
                                </li>
                                <li class="flex items-start space-x-3">
                                    <div class="w-6 h-6 rounded-full bg-[#FFE5CC] flex items-center justify-center flex-shrink-0 mt-0.5">
                                        <img src="https://unpkg.com/lucide-static@latest/icons/check.svg" class="w-4 h-4 text-[#FF9966]" alt="Check">
                                    </div>
                                    <div>
                                        <p class="text-gray-600 font-medium">翻身</p>
                                        <p class="text-sm text-gray-500">5个月时达成</p>
                                    </div>
                                </li>
                                <li class="flex items-start space-x-3">
                                    <div class="w-6 h-6 rounded-full bg-[#FFE5CC] flex items-center justify-center flex-shrink-0 mt-0.5">
                                        <img src="https://unpkg.com/lucide-static@latest/icons/check.svg" class="w-4 h-4 text-[#FF9966]" alt="Check">
                                    </div>
                                    <div>
                                        <p class="text-gray-600 font-medium">抬头</p>
                                        <p class="text-sm text-gray-500">3个月