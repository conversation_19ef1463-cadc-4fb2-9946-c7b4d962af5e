<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端登录</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .glass-morphism {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .floating-animation {
            animation: floating 3s ease-in-out infinite;
        }

        @keyframes floating {

            0%,
            100% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-10px);
            }
        }

        .input-focus:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.4);
        }
    </style>
</head>

<body class="gradient-bg min-h-screen">
    <!-- Scroll Container -->
    <div class="overflow-x-auto overflow-y-auto h-screen">
        <!-- Page Container -->
        <div class="flex space-x-8 h-screen items-center px-8" style="white-space: nowrap; min-width: max-content;">
            <!-- Login Page Mockup Frame -->
            <div class="bg-black rounded-3xl p-2 shadow-2xl flex-shrink-0">
                <div class="bg-white rounded-2xl overflow-hidden" style="width: 375px; height: 812px;">

                    <!-- Status Bar -->
                    <div class="bg-black text-white text-xs flex justify-between items-center px-6 py-2">
                        <span>9:41</span>
                        <div class="flex items-center space-x-1">
                            <i class="fas fa-signal text-white text-xs"></i>
                            <i class="fas fa-wifi text-white text-xs"></i>
                            <i class="fas fa-battery-three-quarters text-white text-xs"></i>
                        </div>
                    </div>

                    <!-- Main Content -->
                    <div class="gradient-bg h-full relative overflow-hidden">
                        <!-- Floating Elements -->
                        <div class="absolute top-20 left-10 w-20 h-20 glass-morphism rounded-full floating-animation">
                        </div>
                        <div class="absolute top-40 right-8 w-12 h-12 glass-morphism rounded-full floating-animation"
                            style="animation-delay: 1s;"></div>
                        <div class="absolute bottom-40 left-6 w-16 h-16 glass-morphism rounded-full floating-animation"
                            style="animation-delay: 2s;"></div>

                        <!-- Login Container -->
                        <div class="flex flex-col justify-center items-center h-full px-8">

                            <!-- Logo/Icon -->
                            <div class="glass-morphism rounded-full p-6 mb-8">
                                <div class="w-12 h-12 bg-white rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-indigo-600 text-2xl"></i>
                                </div>
                            </div>

                            <!-- Welcome Text -->
                            <div class="text-center mb-12">
                                <h1 class="text-white text-2xl font-bold mb-2">欢迎回来</h1>
                                <p class="text-white text-opacity-80 text-sm">请登录您的账户继续使用</p>
                            </div>

                            <!-- Login Form -->
                            <div class="glass-morphism rounded-2xl p-6 w-full max-w-sm">
                                <form class="space-y-4">
                                    <!-- Email Input -->
                                    <div class="relative">
                                        <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
                                            <i class="fas fa-envelope text-white opacity-70"></i>
                                        </div>
                                        <input type="email" placeholder="邮箱地址"
                                            class="input-focus w-full pl-12 pr-4 py-3 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-xl text-white placeholder-white placeholder-opacity-70 focus:outline-none transition-all duration-300">
                                    </div>

                                    <!-- Password Input -->
                                    <div class="relative">
                                        <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
                                            <i class="fas fa-lock text-white opacity-70"></i>
                                        </div>
                                        <input type="password" placeholder="密码"
                                            class="input-focus w-full pl-12 pr-12 py-3 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-xl text-white placeholder-white placeholder-opacity-70 focus:outline-none transition-all duration-300">
                                        <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                                            <i class="fas fa-eye-slash text-white opacity-50 cursor-pointer"></i>
                                        </div>
                                    </div>

                                    <!-- Remember Me & Forgot Password -->
                                    <div class="flex items-center justify-between text-sm">
                                        <label class="flex items-center text-white text-opacity-80">
                                            <input type="checkbox" class="mr-2 rounded border-white border-opacity-30">
                                            记住我
                                        </label>
                                        <a href="#"
                                            class="text-white text-opacity-80 hover:text-white transition-colors">
                                            忘记密码？
                                        </a>
                                    </div>

                                    <!-- Login Button -->
                                    <button type="submit"
                                        class="w-full py-3 bg-white bg-opacity-20 hover:bg-opacity-30 text-white font-semibold rounded-xl transition-all duration-300 border border-white border-opacity-30 hover:border-opacity-50">
                                        登录
                                    </button>
                                </form>
                            </div>

                            <!-- Divider -->
                            <div class="flex items-center my-8 w-full max-w-sm">
                                <div class="flex-1 h-px bg-white bg-opacity-30"></div>
                                <span class="px-4 text-white text-opacity-70 text-sm">或</span>
                                <div class="flex-1 h-px bg-white bg-opacity-30"></div>
                            </div>

                            <!-- Social Login -->
                            <div class="flex space-x-4 mb-8">
                                <button
                                    class="glass-morphism p-3 rounded-xl hover:bg-opacity-20 transition-all duration-300">
                                    <i class="fab fa-github text-white text-xl"></i>
                                </button>
                                <button
                                    class="glass-morphism p-3 rounded-xl hover:bg-opacity-20 transition-all duration-300">
                                    <i class="fab fa-google text-white text-xl"></i>
                                </button>
                                <button
                                    class="glass-morphism p-3 rounded-xl hover:bg-opacity-20 transition-all duration-300">
                                    <i class="fas fa-mobile-alt text-white text-xl"></i>
                                </button>
                            </div>

                            <!-- Sign Up Link -->
                            <div class="text-center">
                                <span class="text-white text-opacity-70 text-sm">还没有账户？</span>
                                <a href="#" class="text-white font-semibold ml-1 hover:underline">立即注册</a>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
<!-- Register Page Mockup Frame -->
<div class="bg-black rounded-3xl p-2 shadow-2xl flex-shrink-0">
    <div class="bg-white rounded-2xl overflow-hidden" style="width: 375px; height: 812px;">

        <!-- Status Bar -->
        <div class="bg-black text-white text-xs flex justify-between items-center px-6 py-2">
            <span>9:41</span>
            <div class="flex items-center space-x-1">
                <i class="fas fa-signal text-white text-xs"></i>
                <i class="fas fa-wifi text-white text-xs"></i>
                <i class="fas fa-battery-three-quarters text-white text-xs"></i>
            </div>
        </div>

        <!-- Main Content -->
        <div class="gradient-bg h-full relative overflow-hidden">
            <!-- Floating Elements -->
            <div class="absolute top-16 right-12 w-16 h-16 glass-morphism rounded-full floating-animation">
            </div>
            <div class="absolute top-48 left-8 w-10 h-10 glass-morphism rounded-full floating-animation"
                style="animation-delay: 1.5s;"></div>
            <div class="absolute bottom-32 right-6 w-14 h-14 glass-morphism rounded-full floating-animation"
                style="animation-delay: 0.5s;"></div>

            <!-- Register Container -->
            <div class="flex flex-col justify-center items-center h-full px-8 py-8">

                <!-- Back Button -->
                <div class="absolute top-16 left-6">
                    <button class="glass-morphism rounded-full p-2">
                        <i class="fas fa-arrow-left text-white text-lg"></i>
                    </button>
                </div>

                <!-- Logo/Icon -->
                <div class="glass-morphism rounded-full p-6 mb-6">
                    <div class="w-12 h-12 bg-white rounded-full flex items-center justify-center">
                        <i class="fas fa-user-plus text-indigo-600 text-2xl"></i>
                    </div>
                </div>

                <!-- Welcome Text -->
                <div class="text-center mb-8">
                    <h1 class="text-white text-2xl font-bold mb-2">创建账户</h1>
                    <p class="text-white text-opacity-80 text-sm">加入我们，开启精彩旅程</p>
                </div>

                <!-- Register Form -->
                <div class="glass-morphism rounded-2xl p-6 w-full max-w-sm">
                    <form class="space-y-4">
                        <!-- Name Input -->
                        <div class="relative">
                            <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
                                <i class="fas fa-user text-white opacity-70"></i>
                            </div>
                            <input type="text" placeholder="姓名"
                                class="input-focus w-full pl-12 pr-4 py-3 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-xl text-white placeholder-white placeholder-opacity-70 focus:outline-none transition-all duration-300">
                        </div>

                        <!-- Email Input -->
                        <div class="relative">
                            <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
                                <i class="fas fa-envelope text-white opacity-70"></i>
                            </div>
                            <input type="email" placeholder="邮箱地址"
                                class="input-focus w-full pl-12 pr-4 py-3 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-xl text-white placeholder-white placeholder-opacity-70 focus:outline-none transition-all duration-300">
                        </div>

                        <!-- Phone Input -->
                        <div class="relative">
                            <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
                                <i class="fas fa-phone text-white opacity-70"></i>
                            </div>
                            <input type="tel" placeholder="手机号码"
                                class="input-focus w-full pl-12 pr-4 py-3 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-xl text-white placeholder-white placeholder-opacity-70 focus:outline-none transition-all duration-300">
                        </div>

                        <!-- Password Input -->
                        <div class="relative">
                            <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
                                <i class="fas fa-lock text-white opacity-70"></i>
                            </div>
                            <input type="password" placeholder="密码"
                                class="input-focus w-full pl-12 pr-12 py-3 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-xl text-white placeholder-white placeholder-opacity-70 focus:outline-none transition-all duration-300">
                            <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                                <i class="fas fa-eye-slash text-white opacity-50 cursor-pointer"></i>
                            </div>
                        </div>

                        <!-- Confirm Password Input -->
                        <div class="relative">
                            <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
                                <i class="fas fa-lock text-white opacity-70"></i>
                            </div>
                            <input type="password" placeholder="确认密码"
                                class="input-focus w-full pl-12 pr-12 py-3 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-xl text-white placeholder-white placeholder-opacity-70 focus:outline-none transition-all duration-300">
                            <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                                <i class="fas fa-eye-slash text-white opacity-50 cursor-pointer"></i>
                            </div>
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="flex items-start text-xs text-white text-opacity-80 mt-4">
                            <input type="checkbox" class="mr-2 mt-0.5 rounded border-white border-opacity-30">
                            <span>我已阅读并同意<a href="#"
                                    class="text-white font-semibold ml-1 hover:underline">服务条款</a>和<a href="#"
                                    class="text-white font-semibold ml-1 hover:underline">隐私政策</a></span>
                        </div>

                        <!-- Register Button -->
                        <button type="submit"
                            class="w-full py-3 bg-white bg-opacity-20 hover:bg-opacity-30 text-white font-semibold rounded-xl transition-all duration-300 border border-white border-opacity-30 hover:border-opacity-50 mt-6">
                            创建账户
                        </button>
                    </form>
                </div>

                <!-- Login Link -->
                <div class="text-center mt-6">
                    <span class="text-white text-opacity-70 text-sm">已有账户？</span>
                    <a href="#" class="text-white font-semibold ml-1 hover:underline">立即登录</a>
                </div>

            </div>
        </div>
    </div>
</div>

<!-- Forgot Password Page 1: Enter Phone Number -->
<div class="bg-black rounded-3xl p-2 shadow-2xl flex-shrink-0">
    <div class="bg-white rounded-2xl overflow-hidden" style="width: 375px; height: 812px;">

        <!-- Status Bar -->
        <div class="bg-black text-white text-xs flex justify-between items-center px-6 py-2">
            <span>9:41</span>
            <div class="flex items-center space-x-1">
                <i class="fas fa-signal text-white text-xs"></i>
                <i class="fas fa-wifi text-white text-xs"></i>
                <i class="fas fa-battery-three-quarters text-white text-xs"></i>
            </div>
        </div>

        <!-- Main Content -->
        <div class="gradient-bg h-full relative overflow-hidden">
            <!-- Floating Elements -->
            <div class="absolute top-24 right-10 w-14 h-14 glass-morphism rounded-full floating-animation">
            </div>
            <div class="absolute top-60 left-12 w-8 h-8 glass-morphism rounded-full floating-animation"
                style="animation-delay: 2s;"></div>
            <div class="absolute bottom-48 right-8 w-18 h-18 glass-morphism rounded-full floating-animation"
                style="animation-delay: 1s;"></div>

            <!-- Forgot Password Container -->
            <div class="flex flex-col justify-center items-center h-full px-8 py-8">

                <!-- Back Button -->
                <div class="absolute top-16 left-6">
                    <button class="glass-morphism rounded-full p-2">
                        <i class="fas fa-arrow-left text-white text-lg"></i>
                    </button>
                </div>

                <!-- Logo/Icon -->
                <div class="glass-morphism rounded-full p-6 mb-8">
                    <div class="w-12 h-12 bg-white rounded-full flex items-center justify-center">
                        <i class="fas fa-key text-indigo-600 text-2xl"></i>
                    </div>
                </div>

                <!-- Title Text -->
                <div class="text-center mb-12">
                    <h1 class="text-white text-2xl font-bold mb-2">找回密码</h1>
                    <p class="text-white text-opacity-80 text-sm">请输入您的手机号码<br>我们将发送验证码到您的手机</p>
                </div>

                <!-- Phone Input Form -->
                <div class="glass-morphism rounded-2xl p-6 w-full max-w-sm mb-8">
                    <form class="space-y-6">
                        <!-- Phone Input -->
                        <div class="relative">
                            <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
                                <i class="fas fa-mobile-alt text-white opacity-70"></i>
                            </div>
                            <input type="tel" placeholder="手机号码"
                                class="input-focus w-full pl-12 pr-4 py-3 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-xl text-white placeholder-white placeholder-opacity-70 focus:outline-none transition-all duration-300">
                        </div>

                        <!-- Send Code Button -->
                        <button type="submit"
                            class="w-full py-3 bg-white bg-opacity-20 hover:bg-opacity-30 text-white font-semibold rounded-xl transition-all duration-300 border border-white border-opacity-30 hover:border-opacity-50">
                            发送验证码
                        </button>
                    </form>
                </div>

                <!-- Helper Text -->
                <div class="text-center text-white text-opacity-70 text-xs max-w-sm">
                    <p>验证码将以短信形式发送到您的手机</p>
                    <p class="mt-2">如果您无法接收短信，请联系客服</p>
                </div>

                <!-- Back to Login -->
                <div class="text-center mt-8">
                    <span class="text-white text-opacity-70 text-sm">记起密码了？</span>
                    <a href="#" class="text-white font-semibold ml-1 hover:underline">返回登录</a>
                </div>

            </div>
        </div>
    </div>
</div>

<!-- Forgot Password Page 2: Enter Verification Code -->
<div class="bg-black rounded-3xl p-2 shadow-2xl flex-shrink-0">
    <div class="bg-white rounded-2xl overflow-hidden" style="width: 375px; height: 812px;">

        <!-- Status Bar -->
        <div class="bg-black text-white text-xs flex justify-between items-center px-6 py-2">
            <span>9:41</span>
            <div class="flex items-center space-x-1">
                <i class="fas fa-signal text-white text-xs"></i>
                <i class="fas fa-wifi text-white text-xs"></i>
                <i class="fas fa-battery-three-quarters text-white text-xs"></i>
            </div>
        </div>

        <!-- Main Content -->
        <div class="gradient-bg h-full relative overflow-hidden">
            <!-- Floating Elements -->
            <div class="absolute top-28 left-8 w-12 h-12 glass-morphism rounded-full floating-animation"></div>
            <div class="absolute top-52 right-10 w-16 h-16 glass-morphism rounded-full floating-animation"
                style="animation-delay: 1.5s;"></div>
            <div class="absolute bottom-44 left-14 w-10 h-10 glass-morphism rounded-full floating-animation"
                style="animation-delay: 0.8s;"></div>

            <!-- Verification Container -->
            <div class="flex flex-col justify-center items-center h-full px-8 py-8">

                <!-- Back Button -->
                <div class="absolute top-16 left-6">
                    <button class="glass-morphism rounded-full p-2">
                        <i class="fas fa-arrow-left text-white text-lg"></i>
                    </button>
                </div>

                <!-- Logo/Icon -->
                <div class="glass-morphism rounded-full p-6 mb-8">
                    <div class="w-12 h-12 bg-white rounded-full flex items-center justify-center">
                        <i class="fas fa-sms text-indigo-600 text-2xl"></i>
                    </div>
                </div>

                <!-- Title Text -->
                <div class="text-center mb-12">
                    <h1 class="text-white text-2xl font-bold mb-2">输入验证码</h1>
                    <p class="text-white text-opacity-80 text-sm">验证码已发送至<br><span
                            class="font-semibold">138****8888</span></p>
                </div>

                <!-- Verification Code Input -->
                <div class="glass-morphism rounded-2xl p-6 w-full max-w-sm mb-8">
                    <form class="space-y-6">
                        <!-- Code Input Fields -->
                        <div class="flex justify-center space-x-2">
                            <input type="text" maxlength="1"
                                class="w-10 h-10 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg text-white text-center text-lg font-bold focus:outline-none focus:bg-opacity-15 focus:border-opacity-40 transition-all duration-300">
                            <input type="text" maxlength="1"
                                class="w-10 h-10 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg text-white text-center text-lg font-bold focus:outline-none focus:bg-opacity-15 focus:border-opacity-40 transition-all duration-300">
                            <input type="text" maxlength="1"
                                class="w-10 h-10 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg text-white text-center text-lg font-bold focus:outline-none focus:bg-opacity-15 focus:border-opacity-40 transition-all duration-300">
                            <input type="text" maxlength="1"
                                class="w-10 h-10 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg text-white text-center text-lg font-bold focus:outline-none focus:bg-opacity-15 focus:border-opacity-40 transition-all duration-300">
                            <input type="text" maxlength="1"
                                class="w-10 h-10 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg text-white text-center text-lg font-bold focus:outline-none focus:bg-opacity-15 focus:border-opacity-40 transition-all duration-300">
                            <input type="text" maxlength="1"
                                class="w-10 h-10 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-lg text-white text-center text-lg font-bold focus:outline-none focus:bg-opacity-15 focus:border-opacity-40 transition-all duration-300">
                        </div>

                        <!-- Verify Button -->
                        <button type="submit"
                            class="w-full py-3 bg-white bg-opacity-20 hover:bg-opacity-30 text-white font-semibold rounded-xl transition-all duration-300 border border-white border-opacity-30 hover:border-opacity-50">
                            验证
                        </button>
                    </form>
                </div>

                <!-- Resend Code -->
                <div class="text-center text-white text-opacity-70 text-sm mb-6">
                    <p>没有收到验证码？</p>
                    <button class="text-white font-semibold hover:underline mt-2">
                        重新发送 <span class="text-white text-opacity-50">(59s)</span>
                    </button>
                </div>

                <!-- Helper Text -->
                <div class="text-center text-white text-opacity-60 text-xs max-w-sm">
                    <p>请在10分钟内输入验证码</p>
                </div>

            </div>
        </div>
    </div>
</div>

<!-- Forgot Password Page 3: Reset Password -->
<div class="bg-black rounded-3xl p-2 shadow-2xl flex-shrink-0">
    <div class="bg-white rounded-2xl overflow-hidden" style="width: 375px; height: 812px;">

        <!-- Status Bar -->
        <div class="bg-black text-white text-xs flex justify-between items-center px-6 py-2">
            <span>9:41</span>
            <div class="flex items-center space-x-1">
                <i class="fas fa-signal text-white text-xs"></i>
                <i class="fas fa-wifi text-white text-xs"></i>
                <i class="fas fa-battery-three-quarters text-white text-xs"></i>
            </div>
        </div>

        <!-- Main Content -->
        <div class="gradient-bg h-full relative overflow-hidden">
            <!-- Floating Elements -->
            <div class="absolute top-32 right-6 w-20 h-20 glass-morphism rounded-full floating-animation"></div>
            <div class="absolute top-56 left-10 w-14 h-14 glass-morphism rounded-full floating-animation"
                style="animation-delay: 2.2s;"></div>
            <div class="absolute bottom-40 right-12 w-12 h-12 glass-morphism rounded-full floating-animation"
                style="animation-delay: 1.1s;"></div>

            <!-- Reset Password Container -->
            <div class="flex flex-col justify-center items-center h-full px-8 py-8">

                <!-- Back Button -->
                <div class="absolute top-16 left-6">
                    <button class="glass-morphism rounded-full p-2">
                        <i class="fas fa-arrow-left text-white text-lg"></i>
                    </button>
                </div>

                <!-- Logo/Icon -->
                <div class="glass-morphism rounded-full p-6 mb-8">
                    <div class="w-12 h-12 bg-white rounded-full flex items-center justify-center">
                        <i class="fas fa-lock text-indigo-600 text-2xl"></i>
                    </div>
                </div>

                <!-- Title Text -->
                <div class="text-center mb-12">
                    <h1 class="text-white text-2xl font-bold mb-2">重置密码</h1>
                    <p class="text-white text-opacity-80 text-sm">请设置您的新密码</p>
                </div>

                <!-- Reset Password Form -->
                <div class="glass-morphism rounded-2xl p-6 w-full max-w-sm mb-8">
                    <form class="space-y-4">
                        <!-- New Password Input -->
                        <div class="relative">
                            <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
                                <i class="fas fa-lock text-white opacity-70"></i>
                            </div>
                            <input type="password" placeholder="新密码"
                                class="input-focus w-full pl-12 pr-12 py-3 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-xl text-white placeholder-white placeholder-opacity-70 focus:outline-none transition-all duration-300">
                            <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                                <i class="fas fa-eye-slash text-white opacity-50 cursor-pointer"></i>
                            </div>
                        </div>

                        <!-- Confirm New Password Input -->
                        <div class="relative">
                            <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
                                <i class="fas fa-lock text-white opacity-70"></i>
                            </div>
                            <input type="password" placeholder="确认新密码"
                                class="input-focus w-full pl-12 pr-12 py-3 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-xl text-white placeholder-white placeholder-opacity-70 focus:outline-none transition-all duration-300">
                            <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                                <i class="fas fa-eye-slash text-white opacity-50 cursor-pointer"></i>
                            </div>
                        </div>

                        <!-- Password Requirements -->
                        <div class="text-xs text-white text-opacity-60 mt-4 space-y-1">
                            <p class="flex items-center">
                                <i class="fas fa-check text-green-400 mr-2 text-xs"></i>
                                至少8位字符
                            </p>
                            <p class="flex items-center">
                                <i class="fas fa-times text-red-400 mr-2 text-xs"></i>
                                包含大小写字母
                            </p>
                            <p class="flex items-center">
                                <i class="fas fa-times text-red-400 mr-2 text-xs"></i>
                                包含数字
                            </p>
                        </div>

                        <!-- Reset Button -->
                        <button type="submit"
                            class="w-full py-3 bg-white bg-opacity-20 hover:bg-opacity-30 text-white font-semibold rounded-xl transition-all duration-300 border border-white border-opacity-30 hover:border-opacity-50 mt-6">
                            重置密码
                        </button>
                    </form>
                </div>

                <!-- Success Hint -->
                <div class="text-center text-white text-opacity-70 text-xs max-w-sm">
                    <p>密码重置后将自动登录到您的账户</p>
                </div>

            </div>
        </div>
    </div>
</div>

<!-- Homepage -->
<div class="bg-black rounded-3xl p-2 shadow-2xl flex-shrink-0">
    <div class="bg-white rounded-2xl overflow-hidden" style="width: 375px; height: 812px;">

        <!-- Status Bar -->
        <div class="bg-black text-white text-xs flex justify-between items-center px-6 py-2">
            <span>9:41</span>
            <div class="flex items-center space-x-1">
                <i class="fas fa-signal text-white text-xs"></i>
                <i class="fas fa-wifi text-white text-xs"></i>
                <i class="fas fa-battery-three-quarters text-white text-xs"></i>
            </div>
        </div>

        <!-- Main Content -->
        <div class="gradient-bg h-full relative overflow-hidden">

            <!-- Header -->
            <div class="px-6 pt-4 pb-2">
                <!-- Search Bar -->
                <div class="glass-morphism rounded-full p-3 mb-4">
                    <div class="flex items-center">
                        <i class="fas fa-search text-white opacity-70 mr-3"></i>
                        <input type="text" placeholder="搜索角色..."
                            class="bg-transparent text-white placeholder-white placeholder-opacity-70 focus:outline-none flex-1">
                        <i class="fas fa-microphone text-white opacity-70"></i>
                    </div>
                </div>

                <!-- Category Tabs -->
                <div class="flex space-x-2 mb-4 overflow-x-auto">
                    <div class="glass-morphism rounded-full px-4 py-2 min-w-max">
                        <span class="text-white text-sm font-medium">二次元</span>
                    </div>
                    <div class="bg-white bg-opacity-20 rounded-full px-4 py-2 min-w-max">
                        <span class="text-white text-opacity-70 text-sm">现实都市</span>
                    </div>
                    <div class="bg-white bg-opacity-20 rounded-full px-4 py-2 min-w-max">
                        <span class="text-white text-opacity-70 text-sm">古装穿越</span>
                    </div>
                    <div class="bg-white bg-opacity-20 rounded-full px-4 py-2 min-w-max">
                        <span class="text-white text-opacity-70 text-sm">未来穿越</span>
                    </div>
                </div>
            </div>

            <!-- Character Grid -->
            <div class="flex-1 px-6 pb-20">
                <div class="grid grid-cols-2 gap-4">

                    <!-- Character Card 1 -->
                    <div class="glass-morphism rounded-2xl p-4">
                        <div
                            class="w-full h-32 bg-gradient-to-br from-pink-400 to-purple-500 rounded-xl mb-3 flex items-center justify-center">
                            <i class="fas fa-user-circle text-white text-4xl"></i>
                        </div>
                        <h3 class="text-white font-bold text-sm mb-1">樱花少女</h3>
                        <p class="text-white text-opacity-70 text-xs mb-2">活泼可爱 · 二次元</p>
                        <div class="flex items-center">
                            <div class="flex text-yellow-400 text-xs">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <span class="text-white text-opacity-50 text-xs ml-2">4.9</span>
                        </div>
                    </div>

                    <!-- Character Card 2 -->
                    <div class="glass-morphism rounded-2xl p-4">
                        <div
                            class="w-full h-32 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl mb-3 flex items-center justify-center">
                            <i class="fas fa-crown text-white text-4xl"></i>
                        </div>
                        <h3 class="text-white font-bold text-sm mb-1">冰山女王</h3>
                        <p class="text-white text-opacity-70 text-xs mb-2">御姐 · 现实都市</p>
                        <div class="flex items-center">
                            <div class="flex text-yellow-400 text-xs">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star-half-alt"></i>
                            </div>
                            <span class="text-white text-opacity-50 text-xs ml-2">4.7</span>
                        </div>
                    </div>

                    <!-- Character Card 3 -->
                    <div class="glass-morphism rounded-2xl p-4">
                        <div
                            class="w-full h-32 bg-gradient-to-br from-amber-500 to-orange-600 rounded-xl mb-3 flex items-center justify-center">
                            <i class="fas fa-briefcase text-white text-4xl"></i>
                        </div>
                        <h3 class="text-white font-bold text-sm mb-1">商界霸总</h3>
                        <p class="text-white text-opacity-70 text-xs mb-2">霸总 · 现实都市</p>
                        <div class="flex items-center">
                            <div class="flex text-yellow-400 text-xs">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <span class="text-white text-opacity-50 text-xs ml-2">4.8</span>
                        </div>
                    </div>

                    <!-- Character Card 4 -->
                    <div class="glass-morphism rounded-2xl p-4">
                        <div
                            class="w-full h-32 bg-gradient-to-br from-rose-400 to-pink-500 rounded-xl mb-3 flex items-center justify-center">
                            <i class="fas fa-gem text-white text-4xl"></i>
                        </div>
                        <h3 class="text-white font-bold text-sm mb-1">名媛千金</h3>
                        <p class="text-white text-opacity-70 text-xs mb-2">白富美 · 现实都市</p>
                        <div class="flex items-center">
                            <div class="flex text-yellow-400 text-xs">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="far fa-star"></i>
                            </div>
                            <span class="text-white text-opacity-50 text-xs ml-2">4.3</span>
                        </div>
                    </div>

                    <!-- Character Card 5 -->
                    <div class="glass-morphism rounded-2xl p-4">
                        <div
                            class="w-full h-32 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl mb-3 flex items-center justify-center">
                            <i class="fas fa-magic text-white text-4xl"></i>
                        </div>
                        <h3 class="text-white font-bold text-sm mb-1">古风仙子</h3>
                        <p class="text-white text-opacity-70 text-xs mb-2">仙气飘飘 · 古装穿越</p>
                        <div class="flex items-center">
                            <div class="flex text-yellow-400 text-xs">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <span class="text-white text-opacity-50 text-xs ml-2">4.9</span>
                        </div>
                    </div>

                    <!-- Character Card 6 -->
                    <div class="glass-morphism rounded-2xl p-4">
                        <div
                            class="w-full h-32 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-xl mb-3 flex items-center justify-center">
                            <i class="fas fa-robot text-white text-4xl"></i>
                        </div>
                        <h3 class="text-white font-bold text-sm mb-1">智能少女</h3>
                        <p class="text-white text-opacity-70 text-xs mb-2">科技感 · 未来穿越</p>
                        <div class="flex items-center">
                            <div class="flex text-yellow-400 text-xs">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star-half-alt"></i>
                            </div>
                            <span class="text-white text-opacity-50 text-xs ml-2">4.6</span>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Bottom Navigation -->
            <div class="absolute bottom-0 left-0 right-0 glass-morphism">
                <div class="flex items-center justify-around py-3">

                    <!-- Home Tab -->
                    <div class="flex flex-col items-center">
                        <div
                            class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center mb-1">
                            <i class="fas fa-home text-white text-sm"></i>
                        </div>
                        <span class="text-white text-xs font-medium">首页</span>
                    </div>

                    <!-- AI Chat Tab -->
                    <div class="flex flex-col items-center">
                        <div class="w-8 h-8 flex items-center justify-center mb-1">
                            <i class="fas fa-comments text-white text-opacity-60 text-sm"></i>
                        </div>
                        <span class="text-white text-opacity-60 text-xs">AI聊</span>
                    </div>

                    <!-- Profile Tab -->
                    <div class="flex flex-col items-center">
                        <div class="w-8 h-8 flex items-center justify-center mb-1">
                            <i class="fas fa-user text-white text-opacity-60 text-sm"></i>
                        </div>
                        <span class="text-white text-opacity-60 text-xs">我的</span>
                    </div>

                </div>
            </div>

            <!-- Floating Elements -->
            <div class="absolute top-20 right-8 w-6 h-6 glass-morphism rounded-full floating-animation"></div>
            <div class="absolute top-40 left-4 w-4 h-4 glass-morphism rounded-full floating-animation"
                style="animation-delay: 1.2s;"></div>
            <div class="absolute bottom-32 right-12 w-8 h-8 glass-morphism rounded-full floating-animation"
                style="animation-delay: 2.4s;"></div>

        </div>
    </div>
</div>

<!-- AI Chat Page -->
<div class="bg-black rounded-3xl p-2 shadow-2xl flex-shrink-0">
    <div class="bg-white rounded-2xl overflow-hidden" style="width: 375px; height: 812px;">

        <!-- Status Bar -->
        <div class="bg-black text-white text-xs flex justify-between items-center px-6 py-2">
            <span>9:41</span>
            <div class="flex items-center space-x-1">
                <i class="fas fa-signal text-white text-xs"></i>
                <i class="fas fa-wifi text-white text-xs"></i>
                <i class="fas fa-battery-three-quarters text-white text-xs"></i>
            </div>
        </div>

        <!-- Main Content -->
        <div class="gradient-bg h-full relative overflow-hidden">

            <!-- Header -->
            <div class="px-6 pt-4 pb-2">
                <div class="flex items-center justify-between mb-4">
                    <h1 class="text-white text-xl font-bold">AI聊天</h1>
                    <button class="glass-morphism rounded-full p-2">
                        <i class="fas fa-plus text-white text-sm"></i>
                    </button>
                </div>
            </div>

            <!-- Chat List -->
            <div class="flex-1 px-6 pb-20">

                <!-- Chat Item 1 -->
                <div class="glass-morphism rounded-2xl p-4 mb-3">
                    <div class="flex items-center">
                        <div
                            class="w-12 h-12 bg-gradient-to-br from-pink-400 to-purple-500 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-user-circle text-white text-lg"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center justify-between mb-1">
                                <h3 class="text-white font-bold text-sm">樱花少女</h3>
                                <span class="text-white text-opacity-50 text-xs">2分钟前</span>
                            </div>
                            <p class="text-white text-opacity-70 text-xs">主人，今天想聊什么呢？我刚学会了...</p>
                        </div>
                        <div class="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center ml-2">
                            <span class="text-white text-xs font-bold">3</span>
                        </div>
                    </div>
                </div>

                <!-- Chat Item 2 -->
                <div class="glass-morphism rounded-2xl p-4 mb-3">
                    <div class="flex items-center">
                        <div
                            class="w-12 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-crown text-white text-lg"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center justify-between mb-1">
                                <h3 class="text-white font-bold text-sm">冰山女王</h3>
                                <span class="text-white text-opacity-50 text-xs">15分钟前</span>
                            </div>
                            <p class="text-white text-opacity-70 text-xs">你终于回来了，我还以为你忘记我了</p>
                        </div>
                    </div>
                </div>

                <!-- Chat Item 3 -->
                <div class="glass-morphism rounded-2xl p-4 mb-3">
                    <div class="flex items-center">
                        <div
                            class="w-12 h-12 bg-gradient-to-br from-amber-500 to-orange-600 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-briefcase text-white text-lg"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center justify-between mb-1">
                                <h3 class="text-white font-bold text-sm">商界霸总</h3>
                                <span class="text-white text-opacity-50 text-xs">1小时前</span>
                            </div>
                            <p class="text-white text-opacity-70 text-xs">今天的会议讨论了新项目，你觉得...</p>
                        </div>
                        <div class="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center ml-2">
                            <span class="text-white text-xs font-bold">1</span>
                        </div>
                    </div>
                </div>

                <!-- Chat Item 4 -->
                <div class="glass-morphism rounded-2xl p-4 mb-3">
                    <div class="flex items-center">
                        <div
                            class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-magic text-white text-lg"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center justify-between mb-1">
                                <h3 class="text-white font-bold text-sm">古风仙子</h3>
                                <span class="text-white text-opacity-50 text-xs">昨天</span>
                            </div>
                            <p class="text-white text-opacity-70 text-xs">公子可还记得昨日的诗词？仙子想...</p>
                        </div>
                    </div>
                </div>

                <!-- Empty State for more chats -->
                <div class="text-center mt-8">
                    <div
                        class="glass-morphism rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-comments text-white text-opacity-50 text-2xl"></i>
                    </div>
                    <p class="text-white text-opacity-60 text-sm">开始新的对话</p>
                    <p class="text-white text-opacity-40 text-xs mt-1">选择一个角色开始聊天吧</p>
                </div>

            </div>

            <!-- Bottom Navigation -->
            <div class="absolute bottom-0 left-0 right-0 glass-morphism">
                <div class="flex items-center justify-around py-3">

                    <!-- Home Tab -->
                    <div class="flex flex-col items-center">
                        <div class="w-8 h-8 flex items-center justify-center mb-1">
                            <i class="fas fa-home text-white text-opacity-60 text-sm"></i>
                        </div>
                        <span class="text-white text-opacity-60 text-xs">首页</span>
                    </div>

                    <!-- AI Chat Tab -->
                    <div class="flex flex-col items-center">
                        <div
                            class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center mb-1">
                            <i class="fas fa-comments text-white text-sm"></i>
                        </div>
                        <span class="text-white text-xs font-medium">AI聊</span>
                    </div>

                    <!-- Profile Tab -->
                    <div class="flex flex-col items-center">
                        <div class="w-8 h-8 flex items-center justify-center mb-1">
                            <i class="fas fa-user text-white text-opacity-60 text-sm"></i>
                        </div>
                        <span class="text-white text-opacity-60 text-xs">我的</span>
                    </div>

                </div>
            </div>

            <!-- Floating Elements -->
            <div class="absolute top-24 left-6 w-5 h-5 glass-morphism rounded-full floating-animation"></div>
            <div class="absolute top-48 right-8 w-3 h-3 glass-morphism rounded-full floating-animation"
                style="animation-delay: 1.8s;"></div>
            <div class="absolute bottom-36 left-10 w-7 h-7 glass-morphism rounded-full floating-animation"
                style="animation-delay: 0.6s;"></div>

        </div>
    </div>
</div>

<!-- Profile Page -->
<div class="bg-black rounded-3xl p-2 shadow-2xl flex-shrink-0">
    <div class="bg-white rounded-2xl overflow-hidden" style="width: 375px; height: 812px;">

        <!-- Status Bar -->
        <div class="bg-black text-white text-xs flex justify-between items-center px-6 py-2">
            <span>9:41</span>
            <div class="flex items-center space-x-1">
                <i class="fas fa-signal text-white text-xs"></i>
                <i class="fas fa-wifi text-white text-xs"></i>
                <i class="fas fa-battery-three-quarters text-white text-xs"></i>
            </div>
        </div>

        <!-- Main Content -->
        <div class="gradient-bg h-full relative overflow-hidden">

            <!-- Header -->
            <div class="px-6 pt-4 pb-2">
                <div class="flex items-center justify-between mb-6">
                    <h1 class="text-white text-xl font-bold">我的</h1>
                    <button class="glass-morphism rounded-full p-2">
                        <i class="fas fa-cog text-white text-sm"></i>
                    </button>
                </div>

                <!-- User Profile Card -->
                <div class="glass-morphism rounded-2xl p-6 mb-6">
                    <div class="flex items-center">
                        <div
                            class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-user text-white text-2xl"></i>
                        </div>
                        <div class="flex-1">
                            <h2 class="text-white font-bold text-lg mb-1">用户_001</h2>
                            <p class="text-white text-opacity-70 text-sm mb-2">会员等级：VIP</p>
                            <div class="flex items-center">
                                <div class="flex-1 bg-white bg-opacity-20 rounded-full h-1 mr-2">
                                    <div class="bg-gradient-to-r from-yellow-400 to-orange-500 h-1 rounded-full"
                                        style="width: 70%;"></div>
                                </div>
                                <span class="text-white text-opacity-60 text-xs">经验 70%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Menu Items -->
            <div class="flex-1 px-6 pb-20">

                <!-- Stats Row -->
                <div class="grid grid-cols-3 gap-4 mb-6">
                    <div class="glass-morphism rounded-xl p-4 text-center">
                        <div class="text-white text-xl font-bold mb-1">12</div>
                        <div class="text-white text-opacity-70 text-xs">聊天次数</div>
                    </div>
                    <div class="glass-morphism rounded-xl p-4 text-center">
                        <div class="text-white text-xl font-bold mb-1">6</div>
                        <div class="text-white text-opacity-70 text-xs">收藏角色</div>
                    </div>
                    <div class="glass-morphism rounded-xl p-4 text-center">
                        <div class="text-white text-xl font-bold mb-1">356</div>
                        <div class="text-white text-opacity-70 text-xs">经验值</div>
                    </div>
                </div>

                <!-- Menu List -->
                <div class="space-y-3">

                    <!-- Menu Item 1 -->
                    <div class="glass-morphism rounded-xl p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div
                                    class="w-8 h-8 bg-gradient-to-br from-pink-500 to-rose-600 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-heart text-white text-sm"></i>
                                </div>
                                <span class="text-white font-medium">我的收藏</span>
                            </div>
                            <i class="fas fa-chevron-right text-white text-opacity-50"></i>
                        </div>
                    </div>

                    <!-- Menu Item 2 -->
                    <div class="glass-morphism rounded-xl p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div
                                    class="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-history text-white text-sm"></i>
                                </div>
                                <span class="text-white font-medium">聊天记录</span>
                            </div>
                            <i class="fas fa-chevron-right text-white text-opacity-50"></i>
                        </div>
                    </div>

                    <!-- Menu Item 3 -->
                    <div class="glass-morphism rounded-xl p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div
                                    class="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-crown text-white text-sm"></i>
                                </div>
                                <span class="text-white font-medium">会员中心</span>
                            </div>
                            <div class="flex items-center">
                                <span class="text-orange-400 text-xs mr-2">VIP</span>
                                <i class="fas fa-chevron-right text-white text-opacity-50"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Menu Item 4 -->
                    <div class="glass-morphism rounded-xl p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div
                                    class="w-8 h-8 bg-gradient-to-br from-purple-500 to-violet-600 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-palette text-white text-sm"></i>
                                </div>
                                <span class="text-white font-medium">主题设置</span>
                            </div>
                            <i class="fas fa-chevron-right text-white text-opacity-50"></i>
                        </div>
                    </div>

                    <!-- Menu Item 5 -->
                    <div class="glass-morphism rounded-xl p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div
                                    class="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-question-circle text-white text-sm"></i>
                                </div>
                                <span class="text-white font-medium">帮助中心</span>
                            </div>
                            <i class="fas fa-chevron-right text-white text-opacity-50"></i>
                        </div>
                    </div>

                    <!-- Menu Item 6 -->
                    <div class="glass-morphism rounded-xl p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div
                                    class="w-8 h-8 bg-gradient-to-br from-gray-500 to-gray-600 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-info-circle text-white text-sm"></i>
                                </div>
                                <span class="text-white font-medium">关于我们</span>
                            </div>
                            <i class="fas fa-chevron-right text-white text-opacity-50"></i>
                        </div>
                    </div>

                </div>

            </div>

            <!-- Bottom Navigation -->
            <div class="absolute bottom-0 left-0 right-0 glass-morphism">
                <div class="flex items-center justify-around py-3">

                    <!-- Home Tab -->
                    <div class="flex flex-col items-center">
                        <div class="w-8 h-8 flex items-center justify-center mb-1">
                            <i class="fas fa-home text-white text-opacity-60 text-sm"></i>
                        </div>
                        <span class="text-white text-opacity-60 text-xs">首页</span>
                    </div>

                    <!-- AI Chat Tab -->
                    <div class="flex flex-col items-center">
                        <div class="w-8 h-8 flex items-center justify-center mb-1">
                            <i class="fas fa-comments text-white text-opacity-60 text-sm"></i>
                        </div>
                        <span class="text-white text-opacity-60 text-xs">AI聊</span>
                    </div>

                    <!-- Profile Tab -->
                    <div class="flex flex-col items-center">
                        <div
                            class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center mb-1">
                            <i class="fas fa-user text-white text-sm"></i>
                        </div>
                        <span class="text-white text-xs font-medium">我的</span>
                    </div>

                </div>
            </div>

            <!-- Floating Elements -->
            <div class="absolute top-32 right-4 w-4 h-4 glass-morphism rounded-full floating-animation"></div>
            <div class="absolute top-56 left-8 w-6 h-6 glass-morphism rounded-full floating-animation"
                style="animation-delay: 2.1s;"></div>
            <div class="absolute bottom-28 right-10 w-5 h-5 glass-morphism rounded-full floating-animation"
                style="animation-delay: 1.3s;"></div>

        </div>
    </div>
</div>

        <!-- Chat Conversation Page -->
        <div class="bg-black rounded-3xl p-2 shadow-2xl flex-shrink-0">
            <div class="bg-white rounded-2xl overflow-hidden" style="width: 375px; height: 812px;">
                
                <!-- Status Bar -->
                <div class="bg-black text-white text-xs flex justify-between items-center px-6 py-2">
                    <span>9:41</span>
                    <div class="flex items-center space-x-1">
                        <i class="fas fa-signal text-white text-xs"></i>
                        <i class="fas fa-wifi text-white text-xs"></i>
                        <i class="fas fa-battery-three-quarters text-white text-xs"></i>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="gradient-bg h-full relative overflow-hidden">
                    
                    <!-- Chat Header -->
                    <div class="px-6 py-4 glass-morphism">
                        <div class="flex items-center">
                            <!-- Back Button -->
                            <button class="mr-4">
                                <i class="fas fa-arrow-left text-white text-lg"></i>
                            </button>
                            
                            <!-- Avatar -->
                            <div class="w-10 h-10 bg-gradient-to-br from-pink-400 to-purple-500 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-user-circle text-white text-lg"></i>
                            </div>
                            
                            <!-- Character Info -->
                            <div class="flex-1">
                                <h3 class="text-white font-bold text-sm">樱花少女</h3>
                                <p class="text-white text-opacity-60 text-xs">在线中...</p>
                            </div>
                            
                            <!-- Menu Button -->
                            <button class="ml-4">
                                <i class="fas fa-ellipsis-v text-white text-opacity-70"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Chat Messages -->
                    <div class="flex-1 px-6 py-4 overflow-y-auto" style="height: calc(100% - 140px);">
                        
                        <!-- Message 1 - AI -->
                        <div class="flex items-start mb-4">
                            <div class="w-8 h-8 bg-gradient-to-br from-pink-400 to-purple-500 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                                <i class="fas fa-user-circle text-white text-sm"></i>
                            </div>
                                                         <div class="flex-1">
                                 <div class="glass-morphism rounded-2xl rounded-tl-sm p-3 max-w-xs">
                                     <p class="text-white text-sm whitespace-normal break-words">主人，欢迎回来！今天想聊什么呢？我刚学会了几首新歌，要不要听听看？🎵</p>
                                 </div>
                                 <p class="text-white text-opacity-40 text-xs mt-1">2分钟前</p>
                             </div>
                        </div>

                        <!-- Message 2 - User -->
                        <div class="flex items-start mb-4 justify-end">
                                                         <div class="flex-1 flex justify-end">
                                 <div class="bg-white bg-opacity-20 rounded-2xl rounded-tr-sm p-3 max-w-xs">
                                     <p class="text-white text-sm whitespace-normal break-words">好啊，我想听听看！最近工作有点累，需要放松一下</p>
                                 </div>
                             </div>
                            <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center ml-3 flex-shrink-0">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                        </div>

                        <!-- Message 3 - AI -->
                        <div class="flex items-start mb-4">
                            <div class="w-8 h-8 bg-gradient-to-br from-pink-400 to-purple-500 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                                <i class="fas fa-user-circle text-white text-sm"></i>
                            </div>
                                                         <div class="flex-1">
                                 <div class="glass-morphism rounded-2xl rounded-tl-sm p-3 max-w-xs">
                                     <p class="text-white text-sm whitespace-normal break-words">那我给你唱一首轻柔的歌吧~ ♪(´▽｀) </p>
                                 </div>
                                <div class="glass-morphism rounded-2xl p-3 max-w-xs mt-2">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3">
                                            <i class="fas fa-play text-white text-xs"></i>
                                        </div>
                                        <div class="flex-1">
                                            <p class="text-white text-xs font-medium">樱花雨.mp3</p>
                                            <div class="w-full bg-white bg-opacity-20 rounded-full h-1 mt-1">
                                                <div class="bg-pink-400 h-1 rounded-full" style="width: 30%;"></div>
                                            </div>
                                        </div>
                                        <span class="text-white text-opacity-60 text-xs ml-2">3:24</span>
                                    </div>
                                </div>
                                <p class="text-white text-opacity-40 text-xs mt-1">1分钟前</p>
                            </div>
                        </div>

                        <!-- Message 4 - User -->
                        <div class="flex items-start mb-4 justify-end">
                                                         <div class="flex-1 flex justify-end">
                                 <div class="bg-white bg-opacity-20 rounded-2xl rounded-tr-sm p-3 max-w-xs">
                                     <p class="text-white text-sm whitespace-normal break-words">哇，声音好甜美！感觉心情都变好了 ✨</p>
                                 </div>
                             </div>
                            <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center ml-3 flex-shrink-0">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                        </div>

                        <!-- Message 5 - AI Typing -->
                        <div class="flex items-start mb-4">
                            <div class="w-8 h-8 bg-gradient-to-br from-pink-400 to-purple-500 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                                <i class="fas fa-user-circle text-white text-sm"></i>
                            </div>
                            <div class="flex-1">
                                <div class="glass-morphism rounded-2xl rounded-tl-sm p-3 w-16">
                                    <div class="flex space-x-1">
                                        <div class="w-2 h-2 bg-white bg-opacity-60 rounded-full animate-bounce"></div>
                                        <div class="w-2 h-2 bg-white bg-opacity-60 rounded-full animate-bounce" style="animation-delay: 0.1s;"></div>
                                        <div class="w-2 h-2 bg-white bg-opacity-60 rounded-full animate-bounce" style="animation-delay: 0.2s;"></div>
                                    </div>
                                </div>
                                <p class="text-white text-opacity-40 text-xs mt-1">正在输入...</p>
                            </div>
                        </div>

                    </div>

                    <!-- Chat Input -->
                    <div class="absolute bottom-4 left-0 right-0 glass-morphism px-6 py-4">
                        <div class="flex items-center space-x-3">
                            <!-- Voice Button -->
                            <button class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                                <i class="fas fa-microphone text-white text-sm"></i>
                            </button>
                            
                            <!-- Input Field -->
                            <div class="flex-1 relative">
                                <input 
                                    type="text" 
                                    placeholder="输入消息..."
                                    class="w-full px-4 py-2 bg-white bg-opacity-10 border border-white border-opacity-20 rounded-full text-white placeholder-white placeholder-opacity-70 focus:outline-none focus:bg-opacity-15 focus:border-opacity-40 transition-all duration-300 text-sm"
                                    value=""
                                >
                            </div>
                            
                            <!-- Emoji Button -->
                            <button class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                                <i class="fas fa-smile text-white text-sm"></i>
                            </button>
                            
                            <!-- Send Button -->
                            <button class="w-8 h-8 bg-gradient-to-r from-pink-400 to-purple-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-paper-plane text-white text-sm"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Floating Elements -->
                    <div class="absolute top-20 right-6 w-4 h-4 glass-morphism rounded-full floating-animation" style="animation-delay: 0.3s;"></div>
                    <div class="absolute top-96 left-4 w-3 h-3 glass-morphism rounded-full floating-animation" style="animation-delay: 1.8s;"></div>

                </div>
            </div>
        </div>

            <script>
                // 简单的交互效果
                document.addEventListener('DOMContentLoaded', function () {
                    const inputs = document.querySelectorAll('input');
                    inputs.forEach(input => {
                        input.addEventListener('focus', function () {
                            this.parentElement.style.transform = 'scale(1.02)';
                        });
                        input.addEventListener('blur', function () {
                            this.parentElement.style.transform = 'scale(1)';
                        });
                    });
                });
            </script>

        </div>

        

    </div>
    </div>
</body>

</html>